from clean import clean_data
from generate import generate_analysis


def main():
    """
    主程序：依次执行数据清理和分析生成
    """
    print("开始执行销售数据处理流程...")
    print("=" * 50)

    # 步骤1: 清理数据
    print("步骤1: 清理原始数据...")
    clean_success = clean_data()

    if not clean_success:
        print("数据清理失败，程序终止。")
        return False

    print("=" * 50)

    # 步骤2: 生成分析数据
    print("步骤2: 生成分析数据...")
    generate_success = generate_analysis()

    if not generate_success:
        print("分析数据生成失败。")
        return False

    print("=" * 50)
    print("所有处理步骤完成！")
    print("输出文件:")
    print("- 清理后数据: 销售明细表-clean.csv")
    print("- 分析数据: 销售明细表-gen.csv")

    return True


if __name__ == "__main__":
    main()
