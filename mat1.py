import pandas as pd
import matplotlib.pyplot as plt

# 读取文件
excel_file = pd.read_csv('销售明细表-gen.csv')

# 获取指定工作表中的数据
df = excel_file.parse('销售明细表-gen (2)')

# 按商品类别列分组，计算成本额、销售额和利润额列的总和
grouped = df.groupby('商品类别')[['成本额', '销售额', '利润额']].sum()

# 设置图片清晰度
plt.rcParams['figure.dpi'] = 300

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['WenQuanYi Zen Hei']

# 绘制堆叠柱状图
fig, ax = plt.subplots(figsize=(10, 6))
bottom = [0] * len(grouped.index)
for column in grouped.columns:
    ax.bar(grouped.index, grouped[column], label=column, bottom=bottom)
    bottom = [bottom[i] + grouped[column].iloc[i] for i in range(len(grouped.index))]

# 设置标题和标签
ax.set_title('不同商品类别的成本额、销售额和利润额堆叠柱状图')
ax.set_xlabel('商品类别')
ax.set_ylabel('金额')
ax.legend()

# 设置 x 轴标签旋转角度
plt.xticks(rotation=90)

plt.show()